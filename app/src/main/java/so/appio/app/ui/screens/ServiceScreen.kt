package so.appio.app.ui.screens

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.MoreHoriz
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Widgets
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExperimentalMaterial3ExpressiveApi
import androidx.compose.material3.FlexibleBottomAppBar
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MediumTopAppBar
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.PullToRefreshDefaults
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import android.util.Log
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.LoadingIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheetProperties
import androidx.compose.material3.SheetValue
import androidx.compose.material3.pulltorefresh.PullToRefreshState
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.res.Configuration
import android.widget.Toast
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.collectAsState
import so.appio.app.BuildConfig
import so.appio.app.MainActivity
import so.appio.app.data.database.DatabaseManager
import so.appio.app.data.entity.notification.Notification
import so.appio.app.data.entity.service.Service
import so.appio.app.ui.components.CachedImage
import so.appio.app.ui.components.FeedbackModal
import so.appio.app.ui.components.NotificationPermissionUI
import so.appio.app.ui.components.PoweredByAppio
import so.appio.app.utils.BrowserUtils
import so.appio.app.utils.DateUtils
import so.appio.app.utils.UrlValidator
import so.appio.app.utils.WidgetDiscoveryManager
import so.appio.app.MyApplication
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.outlined.CheckCircle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.colorResource
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import so.appio.app.ui.widgets.parseStringColor
import so.appio.app.R

@OptIn(ExperimentalMaterial3ExpressiveApi::class)
@ExperimentalMaterial3Api
@Composable
fun ServiceScreen(
    modifier: Modifier = Modifier,
    service: Service,
    viewModel: ServiceViewModel,
    onNotificationClick: ((Notification) -> Unit),
    onBackClick: (() -> Unit)? = null,
) {
    val scrollBehavior = TopAppBarDefaults.exitUntilCollapsedScrollBehavior()

    // Observe notifications from ViewModel - automatically updates when database changes
    val notifications by viewModel.notifications.collectAsState()
    val isRefreshing by viewModel.isRefreshing.collectAsState()
    val isInitialLoading by viewModel.isInitialLoading.collectAsState()
    val hasConfiguredWidgets by viewModel.hasConfiguredWidgets.collectAsState()

    // Observe lifecycle events to refresh widget detection when app resumes
    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                Log.d(MainViewModel.TAG_UI + ":ServiceScreen", "App resumed - refreshing widget detection for service: ${service.id}")
                viewModel.refreshWidgetDetection()
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    val pullToRefreshState = rememberPullToRefreshState()
    val coroutineScope = rememberCoroutineScope()

    // Remember scroll state - will be restored from ViewModel if available
    val listState = rememberLazyListState()

    // State for controlling the preview modal
    var showPreviewModal by remember { mutableStateOf(service.showPreview) }

    // State for controlling the service info modal
    var showServiceInfoModal by remember { mutableStateOf(false) }

    // State for controlling the feedback modal
    var showFeedbackModal by remember { mutableStateOf(false) }

    // Get notification permission manager from MainActivity
    val context = LocalContext.current
    val activity = context as? MainActivity
    val notificationPermissionManager = activity?.getNotificationPermissionManager()

    // Get device ID for modals
    val app = context.applicationContext as MyApplication
    val deviceDataStore = app.deviceRepository.getDeviceDataStore()
    val deviceId by deviceDataStore.deviceId.collectAsState(initial = null)

    // Request notification permissions when preview modal is dismissed
    LaunchedEffect(showPreviewModal) {
        // Only request permissions when preview modal becomes false (was dismissed)
        if (!showPreviewModal) {
            notificationPermissionManager?.request()
        }
    }

    // Restore scroll position when returning to this screen
    LaunchedEffect(notifications.size) {
        val savedPosition = viewModel.getSavedScrollPosition()
        if (savedPosition != null && notifications.isNotEmpty() && !isInitialLoading) {
            Log.d(MainViewModel.TAG_UI + ":ServiceScreen", "Restoring scroll position: $savedPosition")
            try {
                // Small delay to ensure list is rendered
                kotlinx.coroutines.delay(100)
                listState.scrollToItem(
                    index = savedPosition.firstVisibleItemIndex,
                    scrollOffset = savedPosition.firstVisibleItemScrollOffset
                )
                Log.d(MainViewModel.TAG_UI + ":ServiceScreen", "Successfully restored scroll position")
                viewModel.clearSavedScrollPosition()
            } catch (e: Exception) {
                Log.e(MainViewModel.TAG_UI + ":ServiceScreen", "Failed to restore scroll position", e)
            }
        }
    }

    // Save scroll position when clicking on a notification
    val onNotificationClickWithSave: (Notification) -> Unit = { notification ->
        // Find the clicked notification's index in the list
        val clickedIndex = notifications.indexOf(notification)
        val firstVisibleIndex = listState.firstVisibleItemIndex
        val firstVisibleOffset = listState.firstVisibleItemScrollOffset

        // If we can find the clicked item and it's visible, try to save a position
        // that will show the clicked item in a similar position when we return
        if (clickedIndex >= 0) {
            val itemsFromTop = clickedIndex - firstVisibleIndex

            // For items near the bottom, save a position that ensures they'll be fully visible
            val targetIndex = if (clickedIndex >= notifications.size - 2) {
                // For last 2 items, save a position that shows them fully above the bottom bar
                maxOf(0, notifications.size - 4)
            } else {
                // For other items, save the current first visible item position
                firstVisibleIndex
            }

            val targetOffset = if (targetIndex == firstVisibleIndex) firstVisibleOffset else 0

            Log.d(MainViewModel.TAG_UI + ":ServiceScreen", "Clicked item $clickedIndex, saving position: index=$targetIndex, offset=$targetOffset")
            viewModel.saveScrollPosition(targetIndex, targetOffset)
        } else {
            // Fallback to current position
            Log.d(MainViewModel.TAG_UI + ":ServiceScreen", "Fallback: saving current position: index=$firstVisibleIndex, offset=$firstVisibleOffset")
            viewModel.saveScrollPosition(firstVisibleIndex, firstVisibleOffset)
        }

        onNotificationClick(notification)
    }

    // Use ViewModel's refresh method directly
    val onRefresh = viewModel::refresh

    val onDismissPreview: () -> Unit = {
        showPreviewModal = false
        // Update service.showPreview to false in background
        coroutineScope.launch(Dispatchers.IO) {
            try {
                val updatedService = service.copy(showPreview = false)
                val serviceRepository = DatabaseManager.getServiceRepository()
                serviceRepository.updateService(updatedService)
                Log.d(MainViewModel.TAG_UI + ":ServiceScreen", "Updated service showPreview to false for service: ${service.id}")
            } catch (e: Exception) {
                Log.e(MainViewModel.TAG_UI + ":ServiceScreen", "Failed to update service showPreview", e)
            }
        }
    }

    val textColor = parseStringColor(service.textColor)
    val backgroundColor = parseStringColor(service.backgroundColor)

    Scaffold(
        modifier = Modifier.nestedScroll(scrollBehavior.nestedScrollConnection),
        containerColor = backgroundColor ?: MaterialTheme.colorScheme.surface,
        topBar = {
            ServiceHeader(
                service = service,
                scrollBehavior = scrollBehavior,
                onBackClick = onBackClick,
                onServiceInfoClick = { showServiceInfoModal = true },
                textColor = textColor,
                backgroundColor = backgroundColor
            )
        },
        content = { innerPadding ->
            ServiceContent(
                service = service,
                notifications = notifications,
                modifier = Modifier.padding(innerPadding),
                isRefreshing = isRefreshing,
                isInitialLoading = isInitialLoading,
                onRefresh = onRefresh,
                pullToRefreshState = pullToRefreshState,
                onNotificationClick = onNotificationClickWithSave,
                listState = listState,
                hasConfiguredWidgets = hasConfiguredWidgets,
                textColor = textColor,
                backgroundColor = backgroundColor
            )
        },
        bottomBar = {
            ServiceFooter(
                textColor = textColor,
                backgroundColor = backgroundColor,
                notificationCount = notifications.size,
                onRefresh = onRefresh,
            )
        }
    )

    // Show preview modal when service.showPreview is true
    if (showPreviewModal) {
        ServicePreviewModal(
            service = service,
            onDismiss = onDismissPreview,
            textColor = textColor,
            backgroundColor = backgroundColor
        )
    } else {
        // Show notification permission dialog when needed (only when preview modal is not showing)
        NotificationPermissionUI()
    }

    // Show service info modal when requested
    if (showServiceInfoModal) {
        ServiceInfoModal(
            service = service,
            deviceId = deviceId,
            onDismiss = { showServiceInfoModal = false },
            onFeedbackClick = { showFeedbackModal = true },
            textColor = textColor,
            backgroundColor = backgroundColor
        )
    }

    // Show feedback modal when requested
    if (showFeedbackModal) {
        FeedbackModal(
            serviceId = service.id,
            deviceId = deviceId,
            onDismiss = { showFeedbackModal = false }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ServiceHeader(
    modifier: Modifier = Modifier,
    service: Service,
    scrollBehavior: TopAppBarScrollBehavior,
    onBackClick: (() -> Unit)? = null,
    onServiceInfoClick: (() -> Unit)? = null,
    textColor: Color? = null,
    backgroundColor: Color? = null,
) {
    MediumTopAppBar(
        modifier = modifier,
        scrollBehavior = scrollBehavior,
        colors = TopAppBarDefaults.topAppBarColors().let { defaultColors ->
            TopAppBarDefaults.topAppBarColors(
                containerColor = backgroundColor ?: defaultColors.containerColor,
                titleContentColor = textColor ?: defaultColors.titleContentColor,
                navigationIconContentColor = textColor ?: defaultColors.navigationIconContentColor,
                actionIconContentColor = textColor ?: defaultColors.actionIconContentColor
            )
        },
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                CachedImage(
                    url = service.logoURL,
                    contentDescription = "${service.title} logo",
                    size = 32.dp,
                    cornerRadius = 8.dp,
                    showLoadingIndicator = false
                )

                Text(
                    text = service.title,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f, fill = false)
                )
            }
        },
        navigationIcon = if (onBackClick != null) {
            {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            }
        } else {
            {}
        },
        actions = {
            // Always show service info button
            onServiceInfoClick?.let { onClick ->
                IconButton(onClick = onClick) {
                    Icon(
                        imageVector = Icons.Filled.MoreHoriz,
                        contentDescription = "Service Information"
                    )
                }
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterial3ExpressiveApi::class)
@Composable
private fun ServiceContent(
    service: Service,
    modifier: Modifier = Modifier,
    notifications: List<Notification>,
    isRefreshing: Boolean,
    isInitialLoading: Boolean,
    onRefresh: () -> Unit,
    pullToRefreshState: PullToRefreshState,
    onNotificationClick: ((Notification) -> Unit),
    listState: LazyListState,
    hasConfiguredWidgets: Boolean = false,
    textColor: Color? = null,
    backgroundColor: Color? = null,
) {
    val context = LocalContext.current
    val widgetDiscoveryManager = remember { WidgetDiscoveryManager(context) }

    PullToRefreshBox(
        isRefreshing = isRefreshing,
        onRefresh = onRefresh,
        state = pullToRefreshState,
        modifier = modifier,
        indicator = {
            // Position the loading indicator below the widget banner if it's shown
            // Banner height calculation: 8dp (outer top) + 16dp (card top) + ~40dp (content) + 16dp (card bottom) + 8dp (outer bottom) + 8dp (extra spacing) = 94dp
            val shouldShowBanner = !hasConfiguredWidgets && widgetDiscoveryManager.isWidgetPinningSupported()
            val bannerHeight = if (shouldShowBanner) 94.dp else 0.dp
            PullToRefreshDefaults.LoadingIndicator(
                state = pullToRefreshState,
                isRefreshing = isRefreshing,
                // colors reversed on purpose
                color = backgroundColor?.copy(alpha = 0.9f) ?: PullToRefreshDefaults.loadingIndicatorColor,
                // TODO: will be renamed to: indicatorContainerColor
                containerColor = textColor?.copy(alpha = 0.2f) ?: PullToRefreshDefaults.loadingIndicatorContainerColor,
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .offset(y = bannerHeight)
            )
        }
    ) {
        Column {
            // Widget banner at the top
            WidgetBanner(
                service = service,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                hasConfiguredWidgets = hasConfiguredWidgets,
                textColor = textColor,
                backgroundColor = backgroundColor
            )

            when {
                // Show nothing during initial loading (no flash of empty state)
                isInitialLoading -> {
                    Column(
                        modifier = modifier.fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        LoadingIndicator(
                            modifier = Modifier.size(48.dp),
                            color = textColor?.copy(alpha = 0.8f) ?: MaterialTheme.colorScheme.primary
                        )
                    }
                }
                // Show empty state only after initial loading is complete and list is empty
                notifications.isEmpty() -> {
                    EmptyNotificationsState(
                        modifier = Modifier.fillMaxSize(),
                        // onClick = onRefresh,
                        onClick = {
                            BrowserUtils.openUrl(context, service.url.toString())
                        },
                        textColor = textColor,
                        backgroundColor = backgroundColor
                    )
                }
                // Show notifications list
                else -> {
                    LazyColumn(
                        state = listState,
                    ) {
                        items(notifications) { notification ->
                            NotificationRow(
                                notification = notification,
                                onClick = { onNotificationClick(notification) },
                                textColor = textColor,
                                backgroundColor = backgroundColor
                            )
                            HorizontalDivider(
                                color = textColor?.copy(alpha = 0.2f) ?: DividerDefaults.color
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun EmptyNotificationsState(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
    textColor: Color? = null,
    backgroundColor: Color? = null,
) {
    val orientation = LocalConfiguration.current.orientation

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Outlined.CheckCircle,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = colorResource(id = R.color.ios_green)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Setup completed",
            color = (textColor ?: MaterialTheme.colorScheme.onSurface)
        )

        Spacer(modifier = Modifier.height(22.dp))

        Button(
            onClick = onClick,
            modifier = Modifier.height(48.dp),
            shape = RoundedCornerShape(24.dp),
            colors = ButtonDefaults.buttonColors(
                // colors reversed on purpose
                containerColor = textColor ?: MaterialTheme.colorScheme.primary,
                contentColor = backgroundColor ?: MaterialTheme.colorScheme.onPrimary
            ),
        ) {
            Text(
                text = "Done",
                style = MaterialTheme.typography.labelLarge,
                fontWeight = FontWeight.Medium,
            )
        }

//        if (orientation != Configuration.ORIENTATION_LANDSCAPE) {
//            Icon(
//                imageVector = Icons.Outlined.Pageview,
//                contentDescription = null,
//                modifier = Modifier.size(64.dp),
//                tint = (textColor ?: MaterialTheme.colorScheme.onSurface).copy(alpha = 0.2f)
//            )
//
//            Spacer(modifier = Modifier.height(16.dp))
//        }
//
//        Text(
//            text = "No notifications",
//            style = MaterialTheme.typography.headlineMedium,
//            color = (textColor ?: MaterialTheme.colorScheme.onSurface).copy(alpha = 0.2f)
//        )
//
//        Spacer(modifier = Modifier.height(8.dp))
//
//        TextButton(
//            onClick = onRefresh,
//            colors = ButtonDefaults.textButtonColors().let { defaultColors ->
//                ButtonDefaults.textButtonColors(
//                    containerColor = backgroundColor ?: defaultColors.containerColor
//                )
//            }
//        ) {
//            Text("Refresh", color = textColor ?: Color.Unspecified)
//        }
    }
}

@Composable
private fun NotificationRow(
    notification: Notification,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit),
    textColor: Color? = null,
    backgroundColor: Color? = null,
) {
    ListItem(
        modifier = modifier.clickable {
            onClick()
        },
        colors = ListItemDefaults.colors().let { defaultColors ->
            ListItemDefaults.colors(
                containerColor = backgroundColor ?: defaultColors.containerColor
            )
        },
        headlineContent = {
            Text(
                text = notification.title,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = textColor ?: Color.Unspecified
            )
        },
        supportingContent = {
            Text(
                text = notification.body,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                color = textColor ?: Color.Unspecified
            )
        },
        trailingContent = {
            Text(
                text = DateUtils.formatDate(notification.receivedAt),
                style = MaterialTheme.typography.bodySmall,
                color = textColor ?: Color.Unspecified,
                textAlign = TextAlign.End,
            )
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterial3ExpressiveApi::class)
@Composable
private fun ServiceFooter(
    modifier: Modifier = Modifier,
    textColor: Color? = null,
    backgroundColor: Color? = null,
    notificationCount: Int,
    onRefresh: () -> Unit,
) {
    FlexibleBottomAppBar(
        modifier = modifier
            .border(
                width = 1.dp,
                color = textColor?.copy(alpha = 0.1f) ?: MaterialTheme.colorScheme.outline.copy(alpha = 0.2f),
            ),
        // colors reversed on purpose
        containerColor = textColor?.copy(alpha = 0.03f) ?: MaterialTheme.colorScheme.surface,
        content = {
            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "$notificationCount notifications",
                    style = MaterialTheme.typography.bodyMedium,
                    color = textColor ?: MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.align(Alignment.Center)
                )

                IconButton(
                    onClick = onRefresh,
                    modifier = Modifier.align(Alignment.CenterEnd)
                ) {
                    Icon(
                        imageVector = Icons.Filled.Refresh,
                        contentDescription = "Refresh",
                        tint = (textColor ?: MaterialTheme.colorScheme.onSurface).copy(alpha = 0.7f)
                    )
                }
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ServicePreviewModal(
    service: Service,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    textColor: Color? = null,
    backgroundColor: Color? = null,
) {
    val orientation = LocalConfiguration.current.orientation

    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true,
        confirmValueChange = { sheetValue ->
            // Only allow the sheet to be in Expanded state, prevent Hidden state
            sheetValue != SheetValue.Hidden
        }
    )

    ModalBottomSheet(
        onDismissRequest = { /* Do nothing - modal is not dismissible by swipe/click away */ },
        modifier = modifier,
        sheetState = sheetState,
        dragHandle = null, // Remove drag handle to prevent swipe dismissal
        containerColor = backgroundColor ?: BottomSheetDefaults.ContainerColor,
        properties = ModalBottomSheetProperties(
            shouldDismissOnBackPress = false
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 32.dp)
        ) {
            if (orientation != Configuration.ORIENTATION_LANDSCAPE) {
                CachedImage(
                    url = service.bannerURL,
                    contentDescription = "${service.title} banner",
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(16f / 9f), // Standard banner aspect ratio
                    cornerRadius = 0.dp,
                    showLoadingIndicator = true,
                    loadingIndicatorColor = textColor?.copy(alpha = 0.8f) ?: MaterialTheme.colorScheme.primary
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp),
                verticalAlignment = Alignment.Top,
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Service logo
                CachedImage(
                    url = service.logoURL,
                    contentDescription = "${service.title} logo",
                    size = 48.dp,
                    cornerRadius = 4.dp,
                    showLoadingIndicator = false
                )

                // Title and description
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = service.title,
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = textColor ?: MaterialTheme.colorScheme.onSurface
                    )

                    service.description?.let { description ->
                        Text(
                            text = description,
                            style = MaterialTheme.typography.bodyMedium,
                            color = textColor ?: MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Done button - centered horizontally
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                Button(
                    onClick = onDismiss,
                    modifier = Modifier
                        .padding(horizontal = 24.dp)
                        .height(48.dp),
                    shape = RoundedCornerShape(24.dp),
                    colors = ButtonDefaults.buttonColors().let { defaultColors ->
                        ButtonDefaults.buttonColors(
                            // colors reversed on purpose
                            containerColor = textColor ?: defaultColors.containerColor
                        )
                    }
                ) {
                    Text(
                        text = "Done",
                        style = MaterialTheme.typography.labelLarge,
                        fontWeight = FontWeight.Medium,
                        // colors reversed on purpose
                        color = backgroundColor ?: Color.Unspecified,
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ServiceInfoModal(
    service: Service,
    deviceId: String?,
    onDismiss: () -> Unit,
    onFeedbackClick: () -> Unit,
    modifier: Modifier = Modifier,
    textColor: Color? = null,
    backgroundColor: Color? = null,
) {
    val context = LocalContext.current

    // State for showing debug info
    var showDebugInfo by remember { mutableStateOf(false) }

    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true
    )

    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = modifier,
        sheetState = sheetState,
        dragHandle = null, // Remove default drag handle, we'll add it on top of banner
        containerColor = backgroundColor ?: BottomSheetDefaults.ContainerColor,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.7f) // Take up 70% of screen height initially
                .verticalScroll(rememberScrollState())
        ) {
            // Container for banner with drag handle on top
            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                // Banner image - edge to edge with 16:9 aspect ratio
                CachedImage(
                    url = service.bannerURL,
                    contentDescription = "${service.title} banner",
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(16f / 9f),
                    cornerRadius = 0.dp,
                    showLoadingIndicator = true,
                    loadingIndicatorColor = textColor?.copy(alpha = 0.8f) ?: MaterialTheme.colorScheme.primary
                )

                // Drag handle positioned on top of banner
                BottomSheetDefaults.DragHandle(
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                )
            }

            // Content below banner
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 32.dp)
            ) {
                Spacer(modifier = Modifier.height(24.dp))

                // Service logo, title and description
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp),
                    verticalAlignment = Alignment.Top,
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Service logo
                    CachedImage(
                        url = service.logoURL,
                        contentDescription = "${service.title} logo",
                        size = 48.dp,
                        cornerRadius = 4.dp,
                        showLoadingIndicator = false
                    )

                    // Title and description
                    Column(
                        modifier = Modifier.weight(1f),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = service.title,
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = textColor ?: MaterialTheme.colorScheme.onSurface
                        )

                        service.description?.let { description ->
                            Text(
                                text = description,
                                style = MaterialTheme.typography.bodyMedium,
                                color = textColor ?: MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                // Open in Browser button if URL is valid
                val hasValidUrl = UrlValidator.isValidBrowserUrl(service.url)
                if (hasValidUrl) {
                    Spacer(modifier = Modifier.height(24.dp))

                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        Button(
                            onClick = {
                                BrowserUtils.openUrl(context, service.url.toString())
                            },
                            modifier = Modifier.height(48.dp),
                            shape = RoundedCornerShape(24.dp),
                            colors = ButtonDefaults.buttonColors(
                                // colors reversed on purpose
                                containerColor = textColor ?: MaterialTheme.colorScheme.primary,
                                contentColor = backgroundColor ?: MaterialTheme.colorScheme.onPrimary
                            ),
                        ) {
                            Text(
                                text = "Open Website",
                                style = MaterialTheme.typography.labelLarge,
                                fontWeight = FontWeight.Medium,
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))

                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Text(
                        text = "Send Feedback",
                        style = MaterialTheme.typography.bodySmall,
                        color = textColor ?: MaterialTheme.colorScheme.primary,
                        modifier = Modifier
                            .clickable { onFeedbackClick() }
                            .padding(horizontal = 24.dp, vertical = 4.dp),
                        textAlign = TextAlign.Center
                    )

                    PoweredByAppio(
                        serviceId = service.id,
                        textColor = textColor,
                    )

                    // Version text - clickable to show debug info
                    Text(
                        text = "Version: ${BuildConfig.VERSION_NAME}",
                        style = MaterialTheme.typography.bodySmall,
                        color = textColor ?: MaterialTheme.colorScheme.outline,
                        modifier = Modifier
                            .padding(horizontal = 24.dp, vertical = 4.dp)
                            .pointerInput(Unit) {
                                detectTapGestures(
                                    onDoubleTap = {
                                        showDebugInfo = !showDebugInfo
                                    },
                                )
                            },
//                            .clickable { showDebugInfo = !showDebugInfo },
                        textAlign = TextAlign.Center
                    )

                    if (showDebugInfo) {
                        Spacer(modifier = Modifier.height(8.dp))

                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 24.dp)
                        ) {
                            // Device ID - clickable to copy
                            Text(
                                text = deviceId ?: "Not available",
                                style = MaterialTheme.typography.bodySmall,
                                color = textColor ?: MaterialTheme.colorScheme.outline,
                                fontFamily = FontFamily.Monospace,
                                modifier = Modifier.clickable {
                                    deviceId?.let { id ->
                                        copyToClipboard(context, "Device ID", id)
                                    }
                                }
                            )

                            Spacer(modifier = Modifier.height(4.dp))

                            // Service ID - clickable to copy
                            Text(
                                text = service.id,
                                style = MaterialTheme.typography.bodySmall,
                                color = textColor ?: MaterialTheme.colorScheme.outline,
                                fontFamily = FontFamily.Monospace,
                                modifier = Modifier.clickable {
                                    copyToClipboard(context, "Service ID", service.id)
                                }
                            )

                            Spacer(modifier = Modifier.height(4.dp))

                            Text(
                                text = service.customerUserId,
                                style = MaterialTheme.typography.bodySmall,
                                color = textColor ?: MaterialTheme.colorScheme.outline,
                                fontFamily = FontFamily.Monospace,
                                modifier = Modifier.clickable {
                                    copyToClipboard(context, "Customer User ID", service.customerUserId)
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun WidgetBanner(
    service: Service,
    modifier: Modifier = Modifier,
    hasConfiguredWidgets: Boolean = false,
    textColor: Color? = null,
    backgroundColor: Color? = null,
) {
    val context = LocalContext.current
    val widgetDiscoveryManager = remember { WidgetDiscoveryManager(context) }

    // Only show banner if widget pinning is supported AND there are no configured widgets
    if (!hasConfiguredWidgets && widgetDiscoveryManager.isWidgetPinningSupported()) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .let { cardModifier ->
                    if (textColor != null) {
                        cardModifier.border(
                            width = 1.dp,
                            // alpha duplicated so the border will be 20%
                            color = textColor.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(16.dp)
                        )
                    } else {
                        cardModifier
                    }
                },
            colors = CardDefaults.cardColors(
                // colors reversed on purpose
                containerColor = textColor?.copy(alpha = 0.1f) ?: MaterialTheme.colorScheme.primaryContainer
            ),
            shape = RoundedCornerShape(16.dp),

        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Widget icon
                Icon(
                    imageVector = Icons.Filled.Widgets,
                    contentDescription = null,
                    tint = textColor ?: MaterialTheme.colorScheme.onPrimaryContainer,
                    modifier = Modifier.size(32.dp)
                )

                // Text content
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "Try our widget on",
                        style = MaterialTheme.typography.bodyMedium,
                        color = textColor ?: MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Text(
                        text = "your homescreen.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = textColor ?: MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }

                // Show Me button
                Button(
                    onClick = {
                        val success = widgetDiscoveryManager.requestPinWidget(service.id)
                        if (success) {
                            Log.d(MainViewModel.TAG_UI + ":ServiceScreen", "Widget discovery dialog shown for service: ${service.id}")
                        } else {
                            Log.w(MainViewModel.TAG_UI + ":ServiceScreen", "Widget discovery failed for service: ${service.id}")
                        }
                    },
                    colors = ButtonDefaults.buttonColors(
                        // colors reversed on purpose
                        containerColor = textColor ?: MaterialTheme.colorScheme.primary,
                        contentColor = backgroundColor ?: MaterialTheme.colorScheme.onPrimary
                    ),
                    shape = RoundedCornerShape(20.dp)
                ) {
                    Text(
                        text = "Show Me",
                        style = MaterialTheme.typography.labelLarge,
                        fontWeight = FontWeight.Medium,
                    )
                }
            }
        }
    }
}

/**
 * Helper function to copy text to clipboard and show a toast
 */
private fun copyToClipboard(context: Context, label: String, text: String) {
    val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val clipData = ClipData.newPlainText(label, text)
    clipboardManager.setPrimaryClip(clipData)
    Toast.makeText(context, "$label copied to clipboard", Toast.LENGTH_SHORT).show()
}