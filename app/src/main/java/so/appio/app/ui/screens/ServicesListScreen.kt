package so.appio.app.ui.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import so.appio.app.data.entity.service.Service
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.Alignment
import so.appio.app.ui.components.CachedImage
import so.appio.app.ui.theme.AppioAppTheme
import java.util.Date

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.ui.unit.dp
@Composable
fun ServicesListScreen(
    modifier: Modifier = Modifier,
    services: List<Service>,
    onServiceClick: (Service) -> Unit = {},
) {
    val listState = rememberLazyListState()

    Scaffold(
        modifier = modifier
    ) { innerPadding ->
        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding),
            contentPadding = PaddingValues(vertical = 32.dp, horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(32.dp)
        ) {
            items(
                items = services,
                key = { service -> service.id }
            ) { service ->
                ServiceCard(
                    service = service,
                    onClick = { onServiceClick(service) }
                )
            }
        }
    }
}

@Composable
private fun ServiceCard(
    modifier: Modifier = Modifier,
    service: Service,
    onClick: () -> Unit,
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        CachedImage(
            url = service.bannerURL,
            contentDescription = "${service.title} banner",
            cornerRadius = 0.dp,
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(16f / 9f),
        )

        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            CachedImage(
                url = service.logoURL,
                contentDescription = "${service.title} logo",
                cornerRadius = 4.dp,
                size = 32.dp,
                showLoadingIndicator = false,
            )

            // TODO: service textColor and backgroundColor ?
            Text(
                text = service.title,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ServicesListScreenPreview() {
    AppioAppTheme {
        ServicesListScreen(
            services = listOf(
                Service(
                    id = "svc_demo_1",
                    customerUserId = "demo_user_1",
                    title = "Demo Service 1",
                    description = "This is the first demo service",
                    logoURL = "https://cdn.appio.so/app/demo.appio.so/logo.png",
                    bannerURL = "https://cdn.appio.so/app/demo.appio.so/banner.jpg",
                    lastUpdate = Date(),
                    lastSync = Date(),
                ),
                Service(
                    id = "svc_demo_2",
                    customerUserId = "demo_user_2",
                    title = "Demo Service 2",
                    description = "This is the second demo service with a longer description that might wrap to multiple lines",
                    logoURL = "https://cdn.appio.so/app/demo.appio.so/logo.png",
                    lastUpdate = Date(),
                    lastSync = Date(),
                ),
                Service(
                    id = "svc_demo_3",
                    customerUserId = "demo_user_3",
                    title = "Demo Service 3",
                    description = null,
                    logoURL = "https://cdn.appio.so/app/demo.appio.so/logo.png",
                    bannerURL = "https://cdn.appio.so/app/demo.appio.so/banner.jpg",
                    lastUpdate = Date(),
                    lastSync = Date(),
                )
            )
        )
    }
}