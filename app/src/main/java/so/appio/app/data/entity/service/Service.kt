package so.appio.app.data.entity.service

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

/**
 * Service entity representing a service stored in the Room database.
 *
 * @param id Unique identifier for the service (not auto-generated)
 * @param customerUserId Customer user ID associated with this service
 * @param title Service title
 * @param description Optional service description
 * @param logoURL URL for the service logo
 * @param bannerURL Optional URL for the service banner
 * @param url Optional URL for the service
 * @param textColor Optional hex color value for text color
 * @param backgroundColor Optional hex color value for background color
 * @param accentColor Optional hex color value for accent color
 * @param showPreview Whether to show preview for this service, defaults to true for new Service
 * @param lastUpdate Last update timestamp, defaults to current time
 * @param lastSync Last sync timestamp, defaults to current time
 * @param notificationsEnabled Whether notifications are enabled for this service, defaults to true
 */
@Entity(tableName = "services")
data class Service(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,

    @ColumnInfo(name = "customer_user_id")
    val customerUserId: String,

    @ColumnInfo(name = "title")
    val title: String,

    @ColumnInfo(name = "description")
    val description: String? = null,

    @ColumnInfo(name = "logo_url")
    val logoURL: String,

    @ColumnInfo(name = "banner_url")
    val bannerURL: String? = null,

    @ColumnInfo(name = "url")
    val url: String? = null,

    @ColumnInfo(name = "text_color")
    val textColor: String? = null,

    @ColumnInfo(name = "background_color")
    val backgroundColor: String? = null,

    @ColumnInfo(name = "accent_color")
    val accentColor: String? = null,

    @ColumnInfo(name = "show_preview")
    val showPreview: Boolean = true,

    @ColumnInfo(name = "last_update")
    val lastUpdate: Date = Date(),

    @ColumnInfo(name = "last_sync")
    val lastSync: Date = Date(),

    @ColumnInfo(name = "notifications_enabled")
    val notificationsEnabled: Boolean = true,
)
