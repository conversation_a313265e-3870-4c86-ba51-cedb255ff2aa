package so.appio.app.widgets

import android.content.Context
import androidx.glance.GlanceId
import androidx.glance.action.ActionParameters
import androidx.glance.appwidget.action.ActionCallback
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.first
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.MediaType.Companion.toMediaType
import so.appio.app.BuildConfig
import so.appio.app.network.HttpClientConfig
import so.appio.app.data.DeviceDataStore
import so.appio.app.data.database.DatabaseManager

class WidgetButtonAction : ActionCallback {
    companion object {
        private const val TAG = "LOG:WidgetButtonAction"
        const val URL_PARAMETER = "url"
        const val SERVICE_ID_PARAMETER = "serviceId"
    }

    override suspend fun onAction(
        context: Context,
        glanceId: GlanceId,
        parameters: ActionParameters,
    ) {
        val url = parameters[ActionParameters.Key<String>(URL_PARAMETER)]
        val serviceId = parameters[ActionParameters.Key<String>(SERVICE_ID_PARAMETER)]

        if (serviceId.isNullOrBlank()) {
            Log.e(TAG, "WidgetButtonAction called with null or blank serviceId")
            return
        }

        if (url.isNullOrBlank()) {
            Log.d(TAG, "No URL provided, refreshing widget")
            // If no URL provided, just refresh the widget like WidgetRefreshAction
            AppioWidgetUpdateWorker.updateWidgetsForEvent(
                context,
                AppioWidgetUpdateWorker.REASON_MANUAL_REFRESH
            )
            return
        }

        Log.d(TAG, "Making Widget Button URL call to: $url")

        try {
            // Fetch required data for headers
            val deviceId = getDeviceId(context)
            val customerUserId = getCustomerUserId(serviceId)

            val success = callUrl(url, serviceId, deviceId, customerUserId)
            if (success) {
                Log.d(TAG, "Widget Button URL call successful")
                // Trigger widget refresh after successful url call
                AppioWidgetUpdateWorker.updateWidgetsForEvent(
                    context,
                    AppioWidgetUpdateWorker.REASON_MANUAL_REFRESH
                )
            } else {
                Log.e(TAG, "Widget Button URL call failed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error calling URL from WidgetButtonAction", e)
        }
    }

    private suspend fun getDeviceId(context: Context): String? {
        return try {
            val deviceDataStore = DeviceDataStore(context)
            deviceDataStore.deviceId.first()
        } catch (e: Exception) {
            Log.w(TAG, "Failed to get deviceId", e)
            null
        }
    }

    private suspend fun getCustomerUserId(serviceId: String?): String? {
        return try {
            if (serviceId == null) {
                Log.w(TAG, "ServiceId is null, cannot get customerUserId")
                return null
            }

            val serviceRepository = DatabaseManager.getServiceRepository()
            val service = serviceRepository.getServiceById(serviceId)
            service?.customerUserId
        } catch (e: Exception) {
            Log.w(TAG, "Failed to get customerUserId for serviceId: $serviceId", e)
            null
        }
    }

    private suspend fun callUrl(
        urlString: String,
        serviceId: String?,
        deviceId: String?,
        customerUserId: String?
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            val client = HttpClientConfig.createClient()

            val requestBuilder = Request.Builder()
                .url(urlString)
                .get()
                .addHeader("User-Agent", "Appio-Android-Widget-${BuildConfig.VERSION_NAME}")
                .addHeader("X-Service-Id", serviceId ?: "")
                .addHeader("X-Device-Id", deviceId ?: "")
                .addHeader("X-Customer-User-Id", customerUserId ?: "")
                // TODO: widget_id

            Log.d(TAG, "Widget Button URL call: ServiceId: $serviceId, DeviceId: $deviceId, CustomerUserId: $customerUserId")
            val request = requestBuilder.build()
            val response = client.newCall(request).execute()
            val responseCode = response.code
            Log.d(TAG, "Widget Button URL response code: $responseCode")

            response.close()

            // Consider 2xx status codes as successful
            responseCode in 200..299
        } catch (e: Exception) {
            Log.e(TAG, "Exception during Widget Button URL call", e)
            false
        }
    }
}
