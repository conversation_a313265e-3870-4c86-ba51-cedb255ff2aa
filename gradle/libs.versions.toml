# Releases: https://developer.android.com/jetpack/androidx/versions

[versions]
agp = "8.13.0"
coreSplashscreen = "1.0.1"
kotlin = "2.2.20"
ksp = "2.2.0-2.0.2"
coreKtx = "1.17.0"
junit = "4.13.2"
junitVersion = "1.3.0"
espressoCore = "3.7.0"
lifecycleRuntimeKtx = "2.9.3"
activityCompose = "1.11.0"
composeBom = "2025.09.00"
compose-material = "1.7.8"
material3 = "1.5.0-alpha04" # alpha required for LoadingIndicator "1.3.2"
material3-adaptive = "1.1.0" # "1.2.0-alpha06"
glide = "5.0.5"
work = "2.10.4"
navigation = "2.9.4"
datastore = "1.1.7"
glance = "1.1.1"
coreAnimation = "1.0.0"
kotlinSerialization = "1.9.0"
kotlinSerializationPlugin = "2.2.20"
room = "2.8.0"
camerax = "1.5.0"
mlkit-barcode = "17.3.0"
desugar-jdk-libs = "2.1.5"
robolectric = "4.16"
okhttp = "5.1.0"
sentry-plugin = "5.11.0"
window = "1.4.0"
window-core = "1.4.0"
bom = "34.2.0"
installreferrer = "2.2"
media = "1.7.1"
google-services = "4.4.3"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-core-splashscreen = { module = "androidx.core:core-splashscreen", version.ref = "coreSplashscreen" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-lifecycle-runtime-compose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3-icons = { group = "androidx.compose.material3", module = "androidx.compose.material:material-icons-extended", version.ref = "compose-material" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3", version.ref = "material3" }
androidx-material3-adaptive = { group = "androidx.compose.material3.adaptive", name = "adaptive", version.ref = "material3-adaptive" }
androidx-window = { group = "androidx.window", name = "window", version.ref = "window" }
androidx-window-core = { group = "androidx.window", name = "window-core", version.ref = "window-core" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "bom" }
firebase-messaging = { module = "com.google.firebase:firebase-messaging" }
firebase-installations = { module = "com.google.firebase:firebase-installations" }
google-play-services-base = { module = "com.google.android.gms:play-services-base" }
glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide" }
glide-ksp = { group = "com.github.bumptech.glide", name = "ksp", version.ref = "glide" }
glide-okhttp3-integration = { group = "com.github.bumptech.glide", name = "okhttp3-integration", version.ref = "glide" }
androidx-work-runtime-ktx = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "work" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigation" }
androidx-datastore-preferences = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "datastore" }
androidx-glance-appwidget = { group = "androidx.glance", name = "glance-appwidget", version.ref = "glance" }
androidx-glance-material3 = { group = "androidx.glance", name = "glance-material3", version.ref = "glance" }
installreferrer = { module = "com.android.installreferrer:installreferrer", version.ref = "installreferrer" }
androidx-core-animation = { group = "androidx.core", name = "core-animation", version.ref = "coreAnimation" }
androidx-media = { group = "androidx.media", name = "media", version.ref = "media" }
kotlin-serialization = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-core", version.ref = "kotlinSerialization" }
kotlin-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinSerialization" }
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
androidx-camera-core = { group = "androidx.camera", name = "camera-core", version.ref = "camerax" }
androidx-camera-camera2 = { group = "androidx.camera", name = "camera-camera2", version.ref = "camerax" }
androidx-camera-lifecycle = { group = "androidx.camera", name = "camera-lifecycle", version.ref = "camerax" }
androidx-camera-view = { group = "androidx.camera", name = "camera-view", version.ref = "camerax" }
mlkit-barcode-scanning = { group = "com.google.mlkit", name = "barcode-scanning", version.ref = "mlkit-barcode" }
desugar-jdk-libs = { group = "com.android.tools", name = "desugar_jdk_libs", version.ref = "desugar-jdk-libs" }
robolectric = { group = "org.robolectric", name = "robolectric", version.ref = "robolectric" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
google-services = { id = "com.google.gms.google-services", version.ref = "google-services" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlinSerializationPlugin" }
sentry-android = { id = "io.sentry.android.gradle", version.ref = "sentry-plugin" }
